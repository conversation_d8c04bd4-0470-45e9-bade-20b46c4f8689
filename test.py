# ==============================================================================
# 离散 Nelder-Mead 算法实现 - 影响力最大化问题
# ==============================================================================

import numpy as np
import networkx as nx
import random
import time
import os
from typing import List, Set, Tuple, Optional, Dict

# 自定义模块导入
from base_fun import IC, gen_graph
from NM_fun import (
    objective, evaluate_simplex, centroid_discrete,
    reflect_step, expand_step, contract_outside_step, contract_inside_step,
    degree_initialization
)
from plot import plot_evolution_results, create_summary_plot

# ==============================================================================
# 辅助函数
# ==============================================================================

def _summarize_set(s: Set[int], max_items: int = 12) -> str:
    """集合输出摘要：当集合过大时只显示前几个元素"""
    arr = sorted(s)
    if len(arr) <= max_items:
        return f"{arr}"
    return f"{arr[:max_items]} ... 共{len(arr)}个"


def _save_detailed_results_txt(detailed_results: Dict, result_file: str) -> None:
    """保存详细结果到TXT文件"""
    with open(result_file, 'w', encoding='utf-8') as f:
        f.write("=" * 60 + "\n")
        f.write(f"离散Nelder-Mead算法详细结果报告\n")
        f.write("=" * 60 + "\n\n")

        # 基本信息
        f.write("【基本信息】\n")
        f.write(f"网络名称: {detailed_results['network_name']}\n")
        f.write(f"种子集合大小: {detailed_results['parameters']['k']}\n")
        f.write(f"单纯形维度: {detailed_results['parameters']['n']}\n")
        f.write(f"传播概率: {detailed_results['parameters']['p']}\n")
        f.write(f"最大迭代次数: {detailed_results['parameters']['gmax']}\n")
        f.write(f"算法参数: α={detailed_results['parameters']['alpha']}, "
                f"γ={detailed_results['parameters']['gamma']}, "
                f"ρ={detailed_results['parameters']['rho']}\n")
        f.write(f"PRE递推轮数: {detailed_results['parameters']['max_hop']}\n\n")

        # 最终结果
        f.write("【最终结果】\n")
        f.write(f"全局最优适应度: {detailed_results['final_best_fitness']:.6f}\n")
        f.write(f"最优解发现代数: {detailed_results['global_best_generation']}\n")
        if 'ic_evaluation' in detailed_results:
            f.write(f"IC模型估计影响力: {detailed_results['ic_evaluation']['estimated_influence']:.2f}\n")
            f.write(f"蒙特卡洛模拟次数: {detailed_results['ic_evaluation']['monte_carlo_runs']}\n")
        if 'total_runtime' in detailed_results:
            f.write(f"总运行时间: {detailed_results['total_runtime']:.2f}秒\n")
        f.write(f"最优种子集合: {detailed_results['final_best_solution']}\n\n")

        # 进化历史概览
        f.write("【进化历史概览】\n")
        global_best_history = detailed_results['global_best_history']
        f.write(f"初始适应度: {global_best_history[0]:.6f}\n")
        f.write(f"最终适应度: {global_best_history[-1]:.6f}\n")
        f.write(f"适应度提升: {global_best_history[-1] - global_best_history[0]:.6f}\n")

        # 找出所有改进的代数
        improvements = []
        for i in range(1, len(global_best_history)):
            if global_best_history[i] > global_best_history[i-1]:
                improvements.append((i, global_best_history[i]))

        f.write(f"总改进次数: {len(improvements)}\n")
        if improvements:
            f.write("主要改进节点:\n")
            for gen, fitness in improvements[:10]:  # 只显示前10次改进
                f.write(f"  第{gen}代: {fitness:.6f}\n")
        f.write("\n")

        # 每代详细信息（简化版）
        f.write("【每代进化详情】\n")
        f.write("代数\t当前最优\t全局最优\t平均值\t标准差\t是否更新\n")
        f.write("-" * 60 + "\n")

        for gen_info in detailed_results['generations']:
            gen = gen_info['generation']
            current_best = gen_info['current_best_fitness']
            global_best = gen_info['global_best_fitness']
            mean_fitness = gen_info['fitness_stats']['mean']
            std_fitness = gen_info['fitness_stats']['std']
            is_updated = "是" if gen_info['is_global_best_updated'] else "否"

            f.write(f"{gen}\t{current_best:.4f}\t{global_best:.4f}\t"
                   f"{mean_fitness:.4f}\t{std_fitness:.4f}\t{is_updated}\n")

        f.write("\n" + "=" * 60 + "\n")
        f.write("报告生成完成\n")



def _mutate_set(G: nx.Graph, x: Set[int], k: int, m: Optional[int] = None,
                rng: Optional[random.Random] = None) -> Set[int]:
    """
    变异操作：若质心与个体一致或群体退化，则对解作小幅扰动

    Args:
        G: 网络图
        x: 当前种子集合
        k: 目标种子集合大小
        m: 变异规模，默认为种子数的2%
        rng: 随机数生成器

    Returns:
        变异后的种子集合
    """
    if rng is None:
        rng = random
    if m is None:
        m = max(1, int(0.02 * k))  # 默认变异规模为种子数的2%

    # 优化：缓存节点列表，避免重复计算
    if not hasattr(G, '_all_nodes_cache'):
        G._all_nodes_cache = set(G.nodes())
    all_nodes = G._all_nodes_cache

    current = x if isinstance(x, set) else set(x)
    rest = list(all_nodes - current)

    if not rest:
        return current.copy()

    # 计算移除和添加的节点数量
    remove_cnt = min(m, len(current))
    add_cnt = min(m, len(rest))

    # 执行变异操作
    current_list = list(current)
    to_remove = set(rng.sample(current_list, remove_cnt))
    to_add = set(rng.sample(rest, add_cnt))
    mutated = (current - to_remove) | to_add

    # 若因节点总数不足导致大小偏差，做补齐或裁剪
    if len(mutated) > k:
        mutated = set(list(mutated)[:k])
    elif len(mutated) < k:
        extra = list(all_nodes - mutated)
        if extra:
            needed = k - len(mutated)
            mutated |= set(rng.sample(extra, min(needed, len(extra))))

    return mutated


# ==============================================================================
# 主算法：离散 Nelder-Mead 算法
# ==============================================================================

def NM(G: nx.Graph, n: int, k: int, p: float, gmax: int,
       alpha: int = 1, gamma: int = 2, rho: int = 1,
       max_hop: int = 5, random_seed: Optional[int] = None,
       verbose: bool = True, network_name: str = "unknown") -> Tuple[Set[int], List[float], Dict]:
    """
    离散 Nelder–Mead（DM）算法：选择规模为 k 的种子集合，以最大化 PRE 估计的传播

    Args:
        G: 网络图
        n: 单纯形"维度"（单纯形规模为 n+1）
        k: 种子集合大小
        p: 传播概率
        gmax: 最大迭代次数
        alpha: 反射系数
        gamma: 扩展系数
        rho: 收缩系数
        max_hop: PRE 递推轮数
        random_seed: 随机种子
        verbose: 是否输出详细信息
        network_name: 网络名称，用于结果文件命名

    Returns:
        (最优解集合, 每轮迭代的最优适应度序列, 详细结果信息)
    """
    # 设置随机种子
    if random_seed is not None:
        random.seed(random_seed)
        np.random.seed(random_seed)

    # 预计算邻接列表，供 PRE 使用（优化：避免重复转换）
    if not hasattr(G, '_neighbors_cache'):
        G._neighbors_cache = {v: list(G.neighbors(v)) for v in G.nodes()}
    neighbors: Dict[int, List[int]] = G._neighbors_cache

    # 初始化单纯形（n+1 个解，每个为大小为 k 的种子集合）
    simplex = degree_initialization(G, n, k, random_seed=random_seed)
    scored_simplex = evaluate_simplex(G, simplex, p, neighbors, max_hop)

    if verbose:
        print("初始化单纯形完成：")
        init_scores = [float(f) for f, _ in scored_simplex]
        print(f"- 初始适应度范围：min={min(init_scores):.4f} max={max(init_scores):.4f} "
              f"均值={np.mean(init_scores):.4f} std={np.std(init_scores):.6f}")
        print(f"- 初始解数量：{len(scored_simplex)}，每个解大小k={k}")

    best_history: List[float] = []

    # 全局最优个体跟踪
    global_best_fitness = float('-inf')
    global_best_solution = None
    global_best_generation = -1

    # 创建结果保存目录（按网络名称分文件夹）
    result_dir = f"result/{network_name}"
    os.makedirs(result_dir, exist_ok=True)

    # 详细结果记录
    detailed_results = {
        'network_name': network_name,
        'parameters': {
            'n': n, 'k': k, 'p': p, 'gmax': gmax,
            'alpha': alpha, 'gamma': gamma, 'rho': rho, 'max_hop': max_hop
        },
        'generations': [],
        'global_best_history': [],
        'final_best_fitness': None,
        'final_best_solution': None,
        'total_runtime': None
    }

    # 主循环：最多 gmax 轮（每轮对所有个体尝试进化）
    for it in range(gmax):
        # 按适应度降序排列
        scored_simplex.sort(key=lambda t: t[0], reverse=True)
        f_best, x_best = scored_simplex[0]
        f_vals = np.array([f for f, _ in scored_simplex], dtype=float)

        # 更新全局最优个体（确保适应度不会变差）
        if f_best > global_best_fitness:
            global_best_fitness = f_best
            global_best_solution = x_best.copy() if hasattr(x_best, 'copy') else set(x_best)
            global_best_generation = it
            if verbose:
                print(f"*** 发现新的全局最优解！代数={it}, 适应度={f_best:.4f} ***")

        # 记录当前代的最优适应度（可能不是全局最优）
        best_history.append(float(f_best))

        # 记录详细的代信息
        generation_info = {
            'generation': it,
            'current_best_fitness': float(f_best),
            'global_best_fitness': float(global_best_fitness),
            'fitness_stats': {
                'min': float(f_vals.min()),
                'max': float(f_vals.max()),
                'mean': float(f_vals.mean()),
                'std': float(f_vals.std())
            },
            'is_global_best_updated': f_best > global_best_fitness
        }
        detailed_results['generations'].append(generation_info)
        detailed_results['global_best_history'].append(float(global_best_fitness))

        if verbose:
            f_worst = scored_simplex[-1][0]
            print(f"\n【迭代 {it}】最优值={f_best:.4f} 最差值={f_worst:.4f} "
                  f"标准差={f_vals.std():.6f}")

            # 显示前3和后3的适应度
            head = ", ".join([f"{scored_simplex[i][0]:.4f}"
                             for i in range(min(3, len(scored_simplex)))])
            tail = ", ".join([f"{scored_simplex[-i-1][0]:.4f}"
                             for i in range(min(3, len(scored_simplex)))][::-1])
            print(f"- 前3适应度：[{head}]  后3适应度：[{tail}]")
            print(f"- 当前最优解（部分节点）：{_summarize_set(set(x_best))}")

        # 基于当前单纯形，对每个个体独立计算（排除自身）质心并完成一轮进化
        current_scored = list(scored_simplex)
        new_candidates: List[Tuple[float, Set[int]]] = []
        improved_count = 0

        # 预计算所有个体的集合，避免重复转换
        all_sets = [s for _, s in current_scored]

        # 对每个个体进行进化操作
        for j, (f_j, x_j) in enumerate(current_scored):
            # 计算排除自身后的离散质心
            x_c = centroid_discrete(G, all_sets, exclude_set=x_j, k=k)
            D = x_c - x_j

            # 若 D 为空或质心与自身重合，注入小幅变异以避免停滞
            if len(D) == 0:
                x_r = _mutate_set(G, x_j, k)  # 直接传入x_j，避免set转换
                f_r = objective(G, x_r, p, neighbors, max_hop)
            else:
                # 反射操作
                x_r = reflect_step(G, x_c, x_j, alpha=alpha, k=k, verbose=False)
                f_r = objective(G, x_r, p, neighbors, max_hop)

            # 针对个体 j 的阈值（其余个体中的最差）- 优化：避免重复计算
            if j == 0:
                others_scores = [current_scored[i][0] for i in range(1, len(current_scored))]
            elif j == len(current_scored) - 1:
                others_scores = [current_scored[i][0] for i in range(len(current_scored) - 1)]
            else:
                others_scores = [current_scored[i][0] for i in range(len(current_scored)) if i != j]
            f_threshold = min(others_scores) if others_scores else f_j

            # 根据反射结果决定下一步操作
            candidate: Tuple[float, Set[int]]

            if f_r > f_best:
                # 扩展操作
                x_e = expand_step(G, x_r, D, gamma=gamma, k=k, verbose=False) if len(D) > 0 else x_r
                f_e = objective(G, x_e, p, neighbors, max_hop)
                if f_e > f_r:
                    candidate = (f_e, x_e)
                else:
                    candidate = (f_r, x_r)
            elif f_r > f_threshold:
                # 接受反射/变异结果
                candidate = (f_r, x_r)
            else:
                # 收缩操作
                if len(D) > 0 and f_r > f_j:
                    # 外部收缩
                    x_co = contract_outside_step(G, x_c, x_j, rho=rho, k=k, verbose=False)
                    f_co = objective(G, x_co, p, neighbors, max_hop)
                    candidate = (f_co, x_co)
                else:
                    # 内部收缩或再变异
                    if len(D) > 0:
                        x_ci = contract_inside_step(G, x_j, x_c, rho=rho, k=k, verbose=False)
                        f_ci = objective(G, x_ci, p, neighbors, max_hop)
                        candidate = (f_ci, x_ci)
                    else:
                        x_m = _mutate_set(G, x_j, k)  # 直接传入x_j，避免set转换
                        f_m = objective(G, x_m, p, neighbors, max_hop)
                        candidate = (f_m, x_m)

                # 若仍未改进，则保持原个体（避免整体收缩）
                if candidate[0] <= f_j:
                    candidate = (f_j, x_j)

            # 统计改进个体数量
            if candidate[0] > f_j:
                improved_count += 1

            new_candidates.append(candidate)

        # # 若候选完全一致（群体坍缩），对非精英注入少量多样性
        # unique_sets = {tuple(sorted(list(s))) for _, s in new_candidates}
        # if len(unique_sets) == 1 and len(new_candidates) > 1:
        #     best_idx = max(range(len(new_candidates)), key=lambda idx: new_candidates[idx][0])
        #     diversified: List[Tuple[float, Set[int]]] = []
        #     for idx, (f_c, s_c) in enumerate(new_candidates):
        #         if idx == best_idx:
        #             diversified.append((f_c, s_c))
        #         else:
        #             s_mut = _mutate_set(G, set(s_c), k, m=max(1, int(0.01 * k)))
        #             f_mut = objective(G, s_mut, p, neighbors, max_hop)
        #             diversified.append((f_mut, s_mut) if f_mut > f_c else (f_c, s_c))
        #     new_candidates = diversified

        # 统一评估整个人口
        if verbose:
            print(f"- 本轮改进个体数：{improved_count}/{len(current_scored)}")

        scored_simplex = evaluate_simplex(G, [s for _, s in new_candidates], p, neighbors, max_hop)

        if verbose:
            f_vals_new = [f for f, _ in scored_simplex]
            f_vals_array = np.array(f_vals_new)  # 避免重复转换
            print(f"- 轮末适应度范围：min={f_vals_array.min():.4f} max={f_vals_array.max():.4f} "
                  f"均值={f_vals_array.mean():.4f} std={f_vals_array.std():.6f}")
            f_best_new, x_best_new = max(scored_simplex, key=lambda t: t[0])
            print(f"- 更新后新最优值={f_best_new:.4f}，最优解（部分）：{_summarize_set(x_best_new)}")

    # 达到迭代上限，使用全局最优解作为最终结果
    if verbose:
        print("达到最大迭代次数，返回全局最优解。")
        print(f"全局最优适应度：{global_best_fitness:.4f} (发现于第{global_best_generation}代)")
        print(f"全局最优解（部分节点）：{_summarize_set(global_best_solution)}")

    # 确保返回的是set类型
    if not isinstance(global_best_solution, set):
        global_best_solution = set(global_best_solution)

    # 完善详细结果记录
    detailed_results['final_best_fitness'] = float(global_best_fitness)
    detailed_results['final_best_solution'] = sorted(list(global_best_solution))
    detailed_results['global_best_generation'] = global_best_generation

    # 保存详细结果到TXT文件
    result_file = os.path.join(result_dir, f"detailed_results_k{k}.txt")
    _save_detailed_results_txt(detailed_results, result_file)

    # 保存最优解到单独文件
    best_solution_file = os.path.join(result_dir, f"best_solution_k{k}.txt")
    with open(best_solution_file, 'w', encoding='utf-8') as f:
        f.write(f"网络: {network_name}\n")
        f.write(f"种子集合大小: {k}\n")
        f.write(f"最优适应度: {global_best_fitness:.6f}\n")
        f.write(f"发现代数: {global_best_generation}\n")
        f.write(f"最优种子集合: {sorted(list(global_best_solution))}\n")

    if verbose:
        print(f"结果已保存到: {result_dir}")

    return global_best_solution, best_history, detailed_results

# ==============================================================================
# 主函数
# ==============================================================================

def main():
    """主函数：运行离散NM算法求解影响力最大化问题"""
    start_time = time.time()

    # 网络数据路径配置
    # network_path = "D:\\VS\\code\\networks\\netscience.txt"
    # network_path = "D:\\VS\\code\\networks\\blog.txt"
    network_path = "D:\\VS\\code\\networks\\pgp.txt"
    # network_path = "D:\\VS\\code\\networks\\NetHEHT.txt"
    # network_path = "D:\\VS\\code\\networks\\deezer.txt"

    # 提取网络名称
    network_name = os.path.splitext(os.path.basename(network_path))[0]

    # 加载网络图
    g = gen_graph(network_path)
    p = 0.05  # 传播概率

    # 算法参数配置
    n = 30        # 单纯形“维度”（单纯形规模为 n+1）
    k = 100        # 种子集合大小
    gmax = 100    # 最大迭代次数
    alpha = 1     # 反射系数
    gamma = 2     # 扩展系数
    rho = 1       # 收缩系数
    max_hop = 5   # PRE 递推轮数

    print(f"开始运行离散NM算法 - 网络: {network_name}, k={k}")
    print(f"网络规模: {g.number_of_nodes()}个节点, {g.number_of_edges()}条边")

    # 运行离散NM算法求解影响力最大化的种子集合
    seed, best_history, detailed_results = NM(
        g, n, k, p, gmax,
        alpha=alpha, gamma=gamma, rho=rho,
        max_hop=max_hop, verbose=True, network_name=network_name
    )

    # 使用蒙特卡洛IC模型进行最终影响力评估
    mc = 1000
    influence = IC(g, seed, p, mc)

    end_time = time.time()
    runtime = end_time - start_time

    # 更新详细结果中的运行时间和IC评估
    detailed_results['total_runtime'] = runtime
    detailed_results['ic_evaluation'] = {
        'monte_carlo_runs': mc,
        'estimated_influence': float(influence)
    }

    # 输出结果
    print(f"\n=== 最终结果 ===")
    print(f"网络: {network_name}")
    print(f"种子集合大小: {k}")
    print(f"全局最优适应度: {detailed_results['final_best_fitness']:.6f}")
    print(f"IC模型估计影响力: {influence:.2f}")
    print(f"运行时间: {runtime:.2f}秒")
    print(f"最优种子集合: {sorted(seed)}")

    # 创建结果目录（按网络名称分文件夹）
    result_dir = f"result/{network_name}"
    os.makedirs(result_dir, exist_ok=True)
    # 调用绘图函数生成可视化结果
    print("正在生成可视化结果...")

    # 生成主要的进化曲线图（最优个体适应度变化和标准差变化）
    evolution_plot = plot_evolution_results(best_history, detailed_results, network_name, k, result_dir)
    print(f"已保存进化曲线到: {evolution_plot}")

    # 生成算法性能总结图
    summary_plot = create_summary_plot(detailed_results, network_name, k, result_dir)
    print(f"已保存性能总结图到: {summary_plot}")

    # 重新保存更新后的详细结果（TXT格式）
    result_file = os.path.join(result_dir, f"detailed_results_k{k}.txt")
    _save_detailed_results_txt(detailed_results, result_file)

    print(f"所有结果已保存到目录: {result_dir}")

    # plt.show()


if __name__ == "__main__":
    main()