import numpy as np
import networkx as nx
import random
from collections import Counter
from typing import List, Set, Tuple, Optional, Dict
import matplotlib.pyplot as plt

from base_fun import IC, PRE, gen_graph

# ---------------------------
# 离散 NM（DM）各步骤函数
# ---------------------------

from NM_fun import objective, evaluate_simplex, initialize_simplex, centroid_discrete, reflect_step, expand_step,\
      contract_outside_step, contract_inside_step, shrink_step
from NM_fun import degree_initialization

# 辅助：集合输出与差异摘要
def _summarize_set(s: Set[int], max_items: int = 12) -> str:
    arr = sorted(s)
    if len(arr) <= max_items:
        return f"{arr}"
    return f"{arr[:max_items]} ... 共{len(arr)}个"

def _diff_sets(from_set: Set[int], to_set: Set[int]) -> <PERSON><PERSON>[List[int], List[int]]:
    removed = sorted(list(from_set - to_set))
    added = sorted(list(to_set - from_set))
    return removed, added

# 变异：若质心与个体一致或群体退化，则对解作小幅扰动
def _mutate_set(G: nx.Graph, x: Set[int], k: int, m: Optional[int] = None, rng: Optional[random.Random] = None) -> Set[int]:
    if rng is None:
        rng = random
    if m is None:
        m = max(1, int(0.02 * k))  # 默认变异规模为种子数的2%
    current = set(x)
    all_nodes = set(G.nodes())
    rest = list(all_nodes - current)
    if not rest:
        return set(current)
    remove_cnt = min(m, len(current))
    add_cnt = min(m, len(rest))
    to_remove = set(rng.sample(list(current), remove_cnt))
    to_add = set(rng.sample(rest, add_cnt))
    mutated = (current - to_remove) | to_add
    # 若因节点总数不足导致大小偏差，做补齐或裁剪
    if len(mutated) > k:
        mutated = set(list(mutated)[:k])
    elif len(mutated) < k:
        extra = list(all_nodes - mutated)
        if extra:
            mutated |= set(rng.sample(extra, k - len(mutated)))
    return mutated


def NM(G: nx.Graph, n: int, k: int, p: float, gmax: int,
       alpha: int = 1, gamma: int = 2, rho: int = 1, sigma: float = 0.5,
       max_hop: int = 5, random_seed: Optional[int] = None,
       verbose: bool = True) -> Tuple[Set[int], List[float]]:
    """
    离散 Nelder–Mead（DM）算法：选择规模为 k 的种子集合，以最大化 PRE 估计的传播（通过最小化负传播实现）。
    返回：最优解集合 与 每轮迭代的最优适应度序列
    """
    if random_seed is not None:
        random.seed(random_seed)
        np.random.seed(random_seed)

    # 预计算邻接列表，供 PRE 使用
    neighbors: Dict[int, List[int]] = {v: list(G.neighbors(v)) for v in G.nodes()}

    # 第一步：初始化单纯形（n+1 个解，每个为大小为 k 的种子集合）
    simplex = degree_initialization(G, n, k, random_seed=random_seed)
    # 计算初始适应度（PRE 估计）
    scored_simplex = evaluate_simplex(G, simplex, p, neighbors, max_hop)

    if verbose:
        print("初始化单纯形完成：")
        init_scores = [float(f) for f, _ in scored_simplex]
        print(f"- 初始适应度范围：min={min(init_scores):.4f} max={max(init_scores):.4f} 均值={np.mean(init_scores):.4f} std={np.std(init_scores):.6f}")
        print(f"- 初始解数量：{len(scored_simplex)}，每个解大小k={k}")

    best_history: List[float] = []

    # 主循环：最多 gmax 轮（每轮对所有个体尝试进化）
    for it in range(gmax):
        scored_simplex.sort(key=lambda t: t[0], reverse=True)
        f_best, x_best = scored_simplex[0]
        f_vals = np.array([f for f, _ in scored_simplex], dtype=float)

        best_history.append(float(f_best))

        if verbose:
            f_worst = scored_simplex[-1][0]
            print(f"\n【迭代 {it}】最优值={f_best:.4f} 最差值={f_worst:.4f} 标准差={f_vals.std():.6f}")
            head = ", ".join([f"{scored_simplex[i][0]:.4f}" for i in range(min(3, len(scored_simplex)))])
            tail = ", ".join([f"{scored_simplex[-i-1][0]:.4f}" for i in range(min(3, len(scored_simplex)))][::-1])
            print(f"- 前3适应度：[{head}]  后3适应度：[{tail}]")
            print(f"- 当前最优解（部分节点）：{_summarize_set(set(x_best))}")

        # 基于当前单纯形，对每个个体独立计算（排除自身）质心并完成一轮进化
        current_scored = list(scored_simplex)
        new_candidates: List[Tuple[float, Set[int]]] = []
        improved_count = 0

        for j, (f_j, x_j) in enumerate(current_scored):
            # 计算排除自身后的离散质心
            x_c = centroid_discrete(G, [s for _, s in current_scored], exclude_set=x_j, k=k)
            D = x_c - x_j

            # 若 D 为空或质心与自身重合，注入小幅变异以避免停滞
            if len(D) == 0:
                x_r = _mutate_set(G, set(x_j), k)
                f_r = objective(G, x_r, p, neighbors, max_hop)
            else:
                # 反射
                x_r = reflect_step(G, x_c, x_j, alpha=alpha, k=k, verbose=False)
                f_r = objective(G, x_r, p, neighbors, max_hop)

            # 针对个体 j 的阈值（其余个体中的最差）
            others_scores = [f for idx, (f, _) in enumerate(current_scored) if idx != j]
            f_threshold = min(others_scores) if others_scores else f_j

            op_name = "反射"
            candidate: Tuple[float, Set[int]]

            if f_r > f_best:
                # 扩展
                x_e = expand_step(G, x_r, D, gamma=gamma, k=k, verbose=False) if len(D) > 0 else x_r
                f_e = objective(G, x_e, p, neighbors, max_hop)
                if f_e > f_r:
                    candidate = (f_e, x_e)
                    op_name = "扩展"
                else:
                    candidate = (f_r, x_r)
                    op_name = "反射"
            elif f_r > f_threshold:
                # 接受反射/变异
                candidate = (f_r, x_r)
                op_name = "反射" if len(D) > 0 else "变异"
            else:
                # 收缩
                if len(D) > 0 and f_r > f_j:
                    # 外部收缩
                    x_co = contract_outside_step(G, x_c, x_j, rho=rho, k=k, verbose=False)
                    f_co = objective(G, x_co, p, neighbors, max_hop)
                    candidate = (f_co, x_co)
                    op_name = "外部收缩"
                else:
                    # 内部收缩或再变异
                    if len(D) > 0:
                        x_ci = contract_inside_step(G, x_j, x_c, rho=rho, k=k, verbose=False)
                        f_ci = objective(G, x_ci, p, neighbors, max_hop)
                        candidate = (f_ci, x_ci)
                        op_name = "内部收缩"
                    else:
                        x_m = _mutate_set(G, set(x_j), k)
                        f_m = objective(G, x_m, p, neighbors, max_hop)
                        candidate = (f_m, x_m)
                        op_name = "变异"

                # 若仍未改进，则保持原个体（避免整体现收缩）
                if candidate[0] <= f_j:
                    candidate = (f_j, x_j)
                    op_name = "保留"

            if candidate[0] > f_j:
                improved_count += 1

            new_candidates.append(candidate)

        # # 若候选完全一致（群体坍缩），对非精英注入少量多样性
        # unique_sets = {tuple(sorted(list(s))) for _, s in new_candidates}
        # if len(unique_sets) == 1 and len(new_candidates) > 1:
        #     best_idx = max(range(len(new_candidates)), key=lambda idx: new_candidates[idx][0])
        #     diversified: List[Tuple[float, Set[int]]] = []
        #     for idx, (f_c, s_c) in enumerate(new_candidates):
        #         if idx == best_idx:
        #             diversified.append((f_c, s_c))
        #         else:
        #             s_mut = _mutate_set(G, set(s_c), k, m=max(1, int(0.01 * k)))
        #             f_mut = objective(G, s_mut, p, neighbors, max_hop)
        #             diversified.append((f_mut, s_mut) if f_mut > f_c else (f_c, s_c))
        #     new_candidates = diversified

        # 统一评估整个人口
        if verbose:
            print(f"- 本轮改进个体数：{improved_count}/{len(current_scored)}")

        scored_simplex = evaluate_simplex(G, [s for _, s in new_candidates], p, neighbors, max_hop)

        if verbose:
            f_vals_new = [f for f, _ in scored_simplex]
            print(f"- 轮末适应度范围：min={min(f_vals_new):.4f} max={max(f_vals_new):.4f} 均值={np.mean(f_vals_new):.4f} std={np.std(f_vals_new):.6f}")
            f_best_new, x_best_new = max(scored_simplex, key=lambda t: t[0])
            print(f"- 更新后新最优值={f_best_new:.4f}，最优解（部分）：{_summarize_set(set(x_best_new))}")

    # 达到迭代上限，返回当前最优解
    scored_simplex.sort(key=lambda t: t[0], reverse=True)
    if verbose:
        print("达到最大迭代次数，返回当前最优解（适应度最大）。")
        print(f"最终最优适应度：{scored_simplex[0][0]:.4f}")
        print(f"最终最优解（部分节点）：{_summarize_set(set(scored_simplex[0][1]))}")
    return set(scored_simplex[0][1]), best_history

import matplotlib.pyplot as plt
from matplotlib import rcParams
# 设置全局字体为微软雅黑
rcParams['font.sans-serif'] = ['Microsoft YaHei']  # 指定微软雅黑
rcParams['axes.unicode_minus'] = False  # 解决负号显示为方块的问题

def main():

    import time
    start_time = time.time()
    network_path = "D:\\VS\\code\\networks\\netscience.txt"
    # network_path = "D:\\VS\\code\\networks\\blog.txt"
    # network_path = "D:\\VS\\code\\networks\\pgp.txt"
    # network_path = "D:\\VS\\code\\networks\\NetHEHT.txt"
    # network_path = "D:\\VS\\code\\networks\\deezer.txt"

    g = gen_graph(network_path)
    p = 0.05

    # 算法参数（可根据需要调整）
    n = 30           # 单纯形“维度”（单纯形规模为 n+1）
    k = 50           # 种子集合大小
    gmax = 100        # 最大迭代次数
    alpha = 1
    gamma = 2
    rho = 1
    sigma = 0.5

    max_hop = 5      # PRE 递推轮数

    # 运行离散NM以求解影响力最大化的种子集合
    seed, best_history = NM(g, n, k, p, gmax, alpha=alpha, gamma=gamma, rho=rho, sigma=sigma, max_hop=max_hop, verbose=False)

    # 使用蒙特卡洛IC进行一次最终影响力评估与输出（可选）
    mc = 1000
    influence = IC(g, seed, p, mc)
    print(f"\n最终种子集合: {sorted(seed)}")
    print(f"IC模型下的估计平均影响力: {influence:.2f}")

    end_time = time.time()
    print(f"运行时间：{end_time - start_time:.2f}秒")
    # 绘制最优个体的进化曲线（每轮最优适应度）
    plt.figure(figsize=(7, 4))
    plt.plot(range(1, len(best_history) + 1), best_history, marker='o')
    plt.title('最优个体适应度进化曲线')
    plt.xlabel('迭代轮次')
    plt.ylabel('最优适应度（PRE估计）')
    plt.grid(True, linestyle='--', alpha=0.5)
    plt.tight_layout()
    plt.savefig('best_evolution_curve.png', dpi=150)
    print("已保存进化曲线到: best_evolution_curve.png")

    #plt.show()



if __name__ == "__main__":
    main()